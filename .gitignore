# Ignore modules and themes checkout out into sites/default for local development.
node_modules
yarn-error.log
npm-debug.log
.pnp.cjs
.pnp.loader.mjs
.yarnrc.yml
.yarn

# Ignore files generated by IDE's.
.idea
.vscode

# Ignore temp file directories.
.DS_Store
.tmp
.sass-cache

# Ignore build directories.
dist
build

# Ignore coverage directory.
coverage

# Ignore files generated by webpack-bundle-analyzer.
webpack-stats.json

# Local Netlify folder
.netlify
