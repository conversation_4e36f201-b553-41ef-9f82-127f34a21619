{"compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "preact", "strict": true, "baseUrl": ".", "allowJs": true, "outDir": "dist", "sourceMap": true, "module": "esnext", "target": "ES2016", "skipLibCheck": true, "lib": ["ES6", "DOM"], "noImplicitAny": true, "esModuleInterop": true, "resolveJsonModule": true, "moduleResolution": "node", "importsNotUsedAsValues": "preserve", "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["node_modules/@types", "src/@types"], "paths": {"@/*": ["src/*"]}}, "exclude": ["node_modules", "public", "webpack", "config", "coverage"]}