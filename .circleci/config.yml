version: 2.1

orbs:
  aws-cli: circleci/aws-cli@3.1.4

jobs:
  build:
    parameters:
      stage:
        type: string
        default: staging

    docker:
      - image: cimg/node:19.3.0

    environment:
      NODE_ENV: <<parameters.stage>>

    steps:
      - checkout

      # restore cached dependencies
      - restore_cache:
          key: v1-deps-{{ checksum "yarn.lock" }}

      # install dependencies
      - run:
          name: 'Install dependencies'
          command: yarn install

      # save any changes to the cache
      - save_cache:
          key: v1-deps-{{ checksum "yarn.lock" }}
          paths:
            - node_modules

      - run:
          name: 'Run <<parameters.stage>> build'
          command: yarn run build

      - run:
          name: 'Rename output files'
          command: |
            mv dist/assets/js/*.js dist/assets/js/theme.js && mv dist/assets/css/*.css dist/assets/css/theme.css

      - aws-cli/setup

      - run:
          name: 'Push JS to S3'
          command: 'aws s3 cp dist/assets/js s3://greenfig-assets-<<parameters.stage>>/theme/js --recursive --include "*.js" --acl public-read'

      - run:
          name: 'Push CSS to S3'
          command: 'aws s3 cp dist/assets/css s3://greenfig-assets-<<parameters.stage>>/theme/css --recursive --include "*.css" --acl public-read'

workflows:
  prod:
    jobs:
      - build:
          stage: production
          filters:
            tags:
              only: /^v1\.\d*\.\d*/
            branches:
              ignore: /.*/
  staging:
    jobs:
      - build:
          stage: staging
          filters:
            branches:
              only:
                - master
