import dotenv from 'dotenv'
import path from 'path'
import fs from 'fs'

const NODE_ENV = process.env.NODE_ENV || 'production'

if(!NODE_ENV)
	throw new Error(
		'The NODE_ENV environment variable is required but was not specified.'
	)

// https://github.com/bkeepers/dotenv#what-other-env-files-can-i-use
const dotenvFiles = [
	`.env.${NODE_ENV}.local`,
	// Don't include `.env.local` for `test` environment
	// since normally you expect tests to produce the same
	// results for everyone
	NODE_ENV !== 'test' && `.env.local`,
	`.env.${NODE_ENV}`,
	'.env',
].filter(Boolean)

// Load environment variables from .env* files. Suppress warnings using silent
// if this file is missing. dotenv will never modify any environment variables
// that have already been set.  Variable expansion is supported in .env files.
// https://github.com/motdotla/dotenv
// https://github.com/motdotla/dotenv-expand
dotenvFiles.forEach(dotenvFile =>
	{
		if(fs.existsSync(dotenvFile))
		{
			console.info(`Loading environment file: ${dotenvFile}`)
			
			dotenv.config({
				path: dotenvFile,
			})
		}
	})

/**
 * Configuration variables for Webpack.
 * Set your own values here.
 */
const portNumber = 9000 // Port number for the server
const hostName = '0.0.0.0' // Hostname for the server
const jsFileOutput = 'assets/js/[name].js' // JavaScript file name once built 'assets/js/[name].[contenthash].js'
const cssFileOutput = 'assets/css/[name].css' // CSS file name once built 'assets/css/[name].[contenthash].css'

/**
 * Set and export configuration.
 */
export const config = {
	HOST: hostName,
	PORT: portNumber,
	JS_FILE_OUTPUT: jsFileOutput,
	CSS_FILE_OUTPUT: cssFileOutput,
	IS_DEV: process.env.NODE_ENV === 'development',
	"resolve": {
		"alias": {
			"react": "preact/compat",
			"react-dom/test-utils": "preact/test-utils",
			"react-dom": "preact/compat",     // Must be below test-utils
			"react/jsx-runtime": "preact/jsx-runtime"
		},
	}
}
