const HandlebarsPlugin = require("handlebars-webpack-plugin")
const path = require("path")
const fs = require("fs")

// Import Configuration.
import { paths } from '../../configuration'

export const customLoaderTemplate = new HandlebarsPlugin({
	// path to hbs entry file(s). Also supports nested directories if write path.join(process.cwd(), "app", "src",
	// "**", "*.hbs"),
	entry: path.join(paths.src, 'templates/*.hbs'),
	// output path and filename(s). This should lie within the webpacks output-folder
	// if ommited, the input filepath stripped of its extension will be used
	output: path.join(paths.dist, '[name]'),
	// you can also add a [path] variable, which will emit the files with their relative path, like
	// output: path.join(process.cwd(), "build", [path], "[name].html"),
	
	// data passed to main hbs template: `main-template(data)`
	data: {
		SCRIPT_URL: process.env.SCRIPT_URL,
		STYLES_URL: process.env.STYLES_URL,
	},
	
	// globbed path to partials, where folder/filename is unique
	// partials: [
	//   path.join(paths.src, "components", "*", "*.hbs")
	// ],
	
	// register custom helpers. May be either a function or a glob-pattern
	helpers: {
		header: function()
		{
			var content = fs.readFileSync(path.join(paths.src, 'helpers/system-js/index.js'))
			return new HandlebarsPlugin.Handlebars.SafeString(content)
		}
		// projectHelpers: path.join(process.cwd(), "app", "helpers", "*.helper.js")
	},
	
	// hooks
	// getTargetFilepath: function (filepath, outputTemplate) {},
	// getPartialId: function (filePath) {}
	onBeforeSetup: function(Handlebars) {},
	onBeforeAddPartials: function(Handlebars, partialsMap) {},
	onBeforeCompile: function(Handlebars, templateContent) {},
	onBeforeRender: function(Handlebars, data, filename) {},
	onBeforeSave: function(Handlebars, resultHtml, filename) {},
	onDone: function(Handlebars, filename) {}
})
