// Import dependencies.
import MiniCssExtractPlugin from 'mini-css-extract-plugin'

// Import Configuration.
import { config } from '../configuration'

export const scss = {
	test: /\.(sa|sc)ss$/,
	use: [
		{
			loader: MiniCssExtractPlugin.loader
		},
		{
			loader: "css-loader",
			options: {
				importLoaders: 2
			}
		},
		{
			loader: "postcss-loader",
			options: {
				// plugins: config.IS_DEV
                //     ? () => [
                //       require("autoprefixer")({ grid: true }),
                //       purgecss({
                //         content: ['./Views/**/*.cshtml', './Content/js/**/*.js', './Content/apps/**/*.vue']
                //       })
                //     ]
                //     : () => [
                //       require("autoprefixer")({ grid: true }),
                //       purgecss({
                //         content: ['./Views/**/*.cshtml', './Content/js/**/*.js', './Content/apps/**/*.vue']
                //       }),
                //       require("cssnano")()
                //     ]
			}
		},
		{
			loader: "sass-loader"
		}
	]
}
