// Import dependencies.
import { merge } from "webpack-merge";

// Import Configuration.
import { WebpackCommonConfig } from "./common";
import { paths, config } from "./configuration";
import { hotModuleReplacementPlugin, miniCssExtractPlugin } from "./plugins";

/**
 * Default dev server settings.
 */
const devServer = {
  open: false,
  compress: true,
  port: config.PORT,
  host: config.HOST,
  headers: {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, PATCH, OPTIONS",
    "Access-Control-Allow-Headers":
      "X-Requested-With, content-type, Authorization",
  },
  client: {
    progress: true,
    overlay: {
      warnings: false,
      errors: true,
    },
  },
  static: [
    {
      watch: true,
      directory: paths.dist,
    },
  ],
};

/**
 * Plugins for development.
 */
const plugins = [hotModuleReplacementPlugin, miniCssExtractPlugin];

/**
 * Webpack development configuration.
 */
const WebpackConfig = {
  plugins,
  devServer,
  devtool: "cheap-module-source-map",
  mode: "development",
  target: "web",
};

// Export configuration.
export const WebpackDevServerConfig = merge(WebpackCommonConfig, WebpackConfig);
