// Import Configuration.
import {
	nodeEnvironmentPlugin,
	htmlWebpackPlugin,
	copyWebpackPlugin,
	eSLintWebpackPlugin,
	customLoaderTemplate,
} from './plugins'
import { paths, config } from './configuration'
import { css, scss, fonts, images, javaScript, typeScript } from './modules'

/**
 * Entry point for the bundle.
 */
const entry = [
	`${paths.src}/index.ts`,
	`${paths.src}/scss/theme.scss`,
]

console.log('entry', entry)

/**
 * Set output file name and path.
 */
const output = {
	publicPath: '/',
	path: paths.dist,
	filename: config.JS_FILE_OUTPUT,
}

/**
 * Shared plugins.
 */
const plugins = [
	nodeEnvironmentPlugin,
	htmlWebpackPlugin,
	copyWebpackPlugin,
	eSLintWebpackPlugin,
	customLoaderTemplate,
]

/**
 * Shared modules.
 */
const modules = {
	rules: [
		css,
		scss,
		fonts,
		images,
		javaScript,
		typeScript
	],
}

/**
 * Resolve extensions.
 * Alias for @ set to paths.src directory.
 */
const resolve = {
	extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
	alias: {
		'@': paths.src,
	},
}

/**
 * Webpack common configuration.
 */
export const WebpackCommonConfig = {
	entry,
	output,
	plugins,
	resolve,
	module: modules,
	context: __dirname,
	mode: config.IS_DEV ? 'development' : 'production',
}
