{"name": "@greenfig/canvas-embed-client", "target": "web", "private": true, "scripts": {"build-dev": "cross-env NODE_ENV=development webpack --progress --config webpack.config.babel.js", "build-staging": "cross-env NODE_ENV=staging webpack --progress --config webpack.config.babel.js", "build": "cross-env NODE_ENV=${NODE_ENV:-production} webpack --config webpack.config.babel.js", "watch-dev": "cross-env NODE_ENV=development webpack --watch --config webpack.config.babel.js", "check-types": "tsc --project tsconfig.json --noEmit", "serve": "cross-env NODE_ENV=development webpack serve --config webpack.config.dev-server.babel.js", "check-size": "webpack --config webpack.config.babel.js --profile --json > webpack-stats.json && webpack-bundle-analyzer webpack-stats.json", "test": "jest", "test:watch": "yarn test --watch"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "dependencies": {"@babel/core": "^7.15.5", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-private-methods": "^7.16.11", "@babel/plugin-proposal-private-property-in-object": "^7.16.7", "@babel/plugin-transform-react-jsx": "^7.16.7", "@babel/preset-env": "^7.15.8", "@babel/preset-typescript": "^7.16.7", "@babel/register": "^7.15.3", "@types/jest": "^26.0.24", "@types/jquery": "^3.5.13", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "@yarnpkg/sdks": "^2.6.0", "babel-jest": "^27.2.5", "babel-loader": "^8.2.2", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^9.0.1", "core-js": "3", "cross-env": "^7.0.3", "css-loader": "^6.3.0", "css-minimizer-webpack-plugin": "^3.1.1", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "dotenv": "^16.0.3", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jest-formatting": "^3.0.0", "eslint-plugin-prettier": "^3.4.0", "eslint-webpack-plugin": "^3.0.1", "eventemitter3": "^4.0.7", "handlebars-webpack-plugin": "^2.2.1", "html-webpack-plugin": "^5.3.2", "husky": "^4.3.8", "image-minimizer-webpack-plugin": "^2.2.0", "imagemin-gifsicle": "^7.0.0", "imagemin-mozjpeg": "^9.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-svgo": "^9.0.0", "jest": "^26.6.3", "json-loader": "^0.5.7", "lint-staged": "^11.2.4", "mini-css-extract-plugin": "^2.4.3", "node-fetch": "^3.2.1", "node-sass": "^7.0.1", "postcss": "^8.3.9", "postcss-custom-media": "^8.0.0", "postcss-import": "^14.0.2", "postcss-loader": "^6.1.1", "postcss-mixins": "^8.1.0", "postcss-nested": "^5.0.6", "postcss-preset-env": "^6.7.0", "postcss-simple-vars": "^6.0.3", "postcss-url": "^10.1.3", "preact": "^10.6.4", "prettier": "^2.4.1", "sass-loader": "^12.4.0", "style-loader": "^3.3.0", "svgo": "^2.8.0", "terser-webpack-plugin": "^5.1.4", "ts-jest": "^26.5.6", "ts-loader": "^9.2.6", "typescript": "^4.4.4", "webpack": "^5.59.1", "webpack-bundle-analyzer": "^4.4.2", "webpack-cli": "^4.8.0", "webpack-dev-server": "^v4.0.0-beta.3", "webpack-manifest-plugin": "^4.1.1", "webpack-merge": "^5.8.0", "webpack-nano": "^1.1.1"}, "browserslist": ["> 5%", "not dead", "not IE < 11", "not op_mini all"]}