/* Import SCSS Partials */

@import "mixins";

/* Import Fonts */

@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400&display=swap");

@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap');

/* font faces */

// TODO: Update this broken link
//@font-face
//{
//	font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
//	src: url("https://s3.amazonaws.com/ambi-static/fonts/lineto-circular-medium.ttf") format("truetype");
//}

body {

	position: relative;

	&:before {
		content: "";
		display: flex;
		align-items: center;
		justify-content: center;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: white;
		z-index: 50000;
	}

	&:after {
		content: url(../assets/images/spinner.svg);
		display: block;
		max-width: 100%;
		height: auto;
		position: absolute;
		left: 50%;
		top: 50%;
		z-index: 60000;
		margin: -100px 0 0 -103px;
	}

	&.loaded:before,
	&.loaded:after {
		display: none;
		transition: opacity 1s ease-out;
    	opacity: .1;
	}


}

#right-side-wrapper-disabled { * { white-space: normal; } }

/*************************************************
** Mobile Login
*************************************************/

#f1_container {
    height: 100%;
	background-color: black;
    background: url(https://s3.us-west-1.wasabisys.com/greenfig/canvas/login_bkg.svg) center bottom no-repeat, #000000;
	background-size: 50%;

	#login_form {
		background: black;
		padding-bottom: 30px;
	}

	.mobileLogin-Header {
		background-image: url(https://greenfiglms.s3.amazonaws.com/account_1/attachments/35433/icon_lock.png);
		
		width: 60px;
		height: 60px;
		background-size: 60px 60px;
    	min-height: 60px;
		margin: auto;
	}

	.Button--primary {
		border: 1px solid white;
	}

	.forgot-password {
		color: white;
	}	

}

/*************************************************
** Mobile Menu
*************************************************/

#mobileContextNavContainer {

	> span > span > span:first-child {
		padding: 0;
	}

	.fxIji_bWOh, .mobile-menu-item {
		width: 100%;
		padding: 5px 10px 5px 10px;
		margin: 0;
		border-bottom: 1px solid silver;
	}

	span.mobile-menu-item {
		display: block;

		a {
			font-size: 16px!important;
		}

	}

	.mobile-menu-icon {
		display: inline-block;
		height: 17px;
		width: 17px;
		padding-right: 2px!important;
		margin-bottom: -2px;
	}

	.mobile-menu-item.calendar .mobile-menu-icon  {
		content:url('https://s3.us-west-1.wasabisys.com/greenfig/canvas/calendar-icon.svg');
		position: relative;
		
	}

	.mobile-menu-item.support .mobile-menu-icon  {
		content:url('https://s3.us-west-1.wasabisys.com/greenfig/canvas/gear.svg');
		position: relative;
		
	}

	.mobile-menu-item.hub .mobile-menu-icon  {
		content:url('https://s3.us-west-1.wasabisys.com/greenfig/canvas/multiple-hub.svg');
		position: relative;
		
	}

	.mobile-menu-item.recordings .mobile-menu-icon  {
		content:url('https://s3.us-west-1.wasabisys.com/greenfig/canvas/online-video.svg');
		position: relative;
		
	}

}

/*************************************************
** Main Menu
*************************************************/

.ic-app-header__main-navigation {
	padding-top: 10px;
}

.ic-app-header__menu-list {
	li:last-of-type { display: none; }
}

.ic-app-header__logomark-container {
	display: none;
}


/*****************************************************
** Global for all users
*******************************************************/

.section_course.gf_remove .student-assignment-overview {	
	display: none;
}


/*****************************************************
** Courses for role 'admin/root-admin/teacher'
*****************************************************/
body:not(.home).pages.user_role_root_admin,
body:not(.home).pages.user_role_admin
body:not(.home).pages.user_role_root_teacher
{
	#right-side-wrapper { display: none; }
}

/*****************************************************
** User Type Student/Observer/TA
*****************************************************/

.user_type_student, .user_type_teacher, .user_type_observer {

	&.subsection_modules {
		.ig-details .points_possible_display,
		.ig-details .due_date_display::after {
			display: none;
		}
	}
	
	&.section_course.gf_remove.playbook .student-assignment-overview {	
		display: block;
	}

}

/*****************************************************
** User Type Student/Observer
*****************************************************/

.user_type_student, .user_type_observer, .is-masquerading-or-student-view {
	
	&.section_course.gf_remove.playbook .student-assignment-overview,
	&.section_course.gf_remove.trailhead .student-assignment-overview {	
		display: block;
	}

}


/*****************************************************
** User Type Observer
*****************************************************/

.user_type_observer {

	a[href*='data_exports'] {
		display: none!important;
	}

}

/*****************************************************
** User Type Teacher Assistant
*****************************************************/

.user_type_teacher {

	&.section_root #right-side-wrapper .ic-sidebar-logo,
	&.section_course #right-side-wrapper .course-options,
	&.section_course #right-side-wrapper a[href*="view=notifications"],
	&.subsection_assignments_syllabus #right-side-wrapper,
	.ic-notification,	
	a.collaborations,
	a.rubrics,
	//a.pages,
	a.quizzes,
	a.files,
	a.rubrics,
	a.settings,
	a.outcomes,
	//Grades
	&[class*="subsection_grades"] #right-side-wrapper,
	&[class*="subsection_grades"] #grades_summary .possible,
	&[class*="subsection_grades"] #grades_summary .points_possible,
	&[class*="subsection_grades"] #grades_summary .toggle_score_details_link,
	&[class*="subsection_grades"] #grades_summary .toggle_final_grade_info,
	&[class*="subsection_grades"] #grades_summary .submission-late-pill,
	&[class*="subsection_grades"] #grades_summary .group_total,
	&[class*="subsection_grades"] #grades_summary .final_grade
	{
		display: none;
	}

	&[class*="subsection_grades"] #grades_summary thead tr > th:nth-child(6) {

		text-align: right;

		&:after {
			content: "Comments";
			display: block;
			position: relative;
			float: right;
		}

	}

	&[class*="subsection_grades"] #grades_summary .submission-missing-pill > span > div,
	.submission_details .submission-missing-pill > span > div {
		color: #0374b5;
		border-color: #0374b5;
	}	

	#assignments:before {

		display: block;
		height: auto;

		@media (min-width: 800px) {
			content: url('https://s3.us-west-1.wasabisys.com/greenfig/canvas/Legend.svg');
			max-width: 600px;
		}
		@media (max-width: 799px) {
			content: url('https://s3.us-west-1.wasabisys.com/greenfig/canvas/Legend_Mobile.svg');
			max-width: 400px;
		}		
		
	}

}



/*****************************************************
** User Type Student/Observer
*****************************************************/
.user_type_student, .user_type_user, .user_type_observer, .is-masquerading-or-student-view {

	&.section_root #right-side-wrapper,
	&.section_courses #right-side-wrapper,
	&.section_files #right-side-wrapper,
	&.section_course #right-side-wrapper,
	&.section_course .student-assignment-overview li:nth-child(2),
	&.section_files #right-side-wrapper,
	&.section_dashboard #right-side-wrapper,
	.tray-with-space-for-global-nav a[href$='/communication'],
	#section-tabs a[href$='/communication'],
	.ic-notification,
	&.subsection_pages_session-recordings .header-bar-outer-container,
	&.subsection_grades #grades_summary .possible,
	&.subsection_grades #grades_summary .points_possible,
	&.subsection_grades #grades_summary .toggle_score_details_link,
	&.subsection_grades #grades_summary .toggle_final_grade_info,
	&.subsection_grades #grades_summary .submission-late-pill,
	&.subsection_grades #grades_summary .group_total,
	&.subsection_grades #grades_summary .final_grade,
	&.subsection_grades #grades_summary thead > tr > th:nth-child(5)
	{
		display: none!important;
	}

	/* Let's Hide the Section Numbers */
	&.subsection_pages_recordings-01,
	&.subsection_pages_recordings-02,
	&.subsection_pages_recordings-03,
	&.subsection_pages_recordings-04,
	&.subsection_pages_recordings-05 {

		#breadcrumbs > ul > li:last-of-type {

			span {
				display: none;
			}
	
			&:after {
				content: "Recordings";
				display: inline-block;
			}
			
		}

		.page-title {
			text-indent: -9999px;
			&:after {
				content: "Recordings";
				text-indent: 0;
				float: left;
			}
		}

	}
	
	/* Let's Swap Course Buttons */
	[data-section="1"] {}
	[data-section="2"] {}
	[data-section="3"] {}
	[data-section="4"] {}


	&.subsection_grades #grades_summary thead tr > th:nth-child(6) {

		text-align: right;

		&:after {
			content: "Comments";
			display: block;
			position: relative;
			float: right;
		}

	}

	&.profile_settings #right-side-wrapper > aside {
		h2:first-of-type, div:first-of-type, hr { 
			display: none; 
		}
	}

	&.subsection_grades #grades_summary .submission-missing-pill > span > div,
	.submission_details .submission-missing-pill > span > div{
		color: #0374b5!important;
		border-color: #0374b5!important;
	}	

	#assignments:before {

		display: block;
		height: auto;

		@media (min-width: 800px) {
			content: url('https://s3.us-west-1.wasabisys.com/greenfig/canvas/Legend.svg');
			max-width: 600px;
		}
		@media (max-width: 799px) {
			content: url('https://s3.us-west-1.wasabisys.com/greenfig/canvas/Legend_Mobile.svg');
			max-width: 400px;
		}		
		
	}

	button.submit_assignment_link:not(.submit_assignment_link.late) {

		visibility: hidden;
		&:after {
			content: "Submit Assignment";
			text-indent: 0;
			display: inline-block;
			background: black;
			padding: 5px;
			border-radius: 5px;
			visibility: visible;
			border-radius: 3px;
			transition: background-color 0.2s ease-in-out;
			display: inline-block;
			position: relative;
			padding: 8px 14px;
			margin-bottom: 0;
			font-size: 16px;
			font-size: 1rem;
			line-height: 20px;
			text-align: center;
			vertical-align: middle;
			cursor: pointer;
			text-decoration: none;
			overflow: hidden;
			text-shadow: none;
			-webkit-user-select: none;
			user-select: none;
		}		

	}

	&.assignment-submitted button.submit_assignment_link,
	&.assignment-submitted .ic-Action-header__Secondary a {		

		visibility: hidden;
		&:after {
			content: "Re-Submit Assignment"!important;
			text-indent: 0;
			display: inline-block;
			background: black;
			padding: 5px;
			border-radius: 5px;
			visibility: visible;
			border-radius: 3px;
			transition: background-color 0.2s ease-in-out;
			display: inline-block;
			position: relative;
			padding: 8px 14px;
			margin-bottom: 0;
			font-size: 16px;
			font-size: 1rem;
			line-height: 20px;
			text-align: center;
			vertical-align: middle;
			cursor: pointer;
			text-decoration: none;
			overflow: hidden;
			text-shadow: none;
			-webkit-user-select: none;
			user-select: none;
		}		

	}

}

/*****************************************************
** User Type Student / Teacher
*****************************************************/



/*****************************************************
** User Type Student ONLY
*****************************************************/

.user_type_student {

	#DashboardOptionsMenu_Container,
	#create_new_event_link,
	.profile-tab-files,
	.profile-tab-eportfolios,
	.profile-tab-past_global_announcements,
	&.section_profile .ic-Layout-contentMain > h2:first-of-type,
	&.section_profile .ic-Layout-contentMain > h2:first-of-type + p,
	&.section_profile .ic-Layout-contentMain > h2:nth-of-type(2),
	&.section_profile .ic-Layout-contentMain > h2:nth-of-type(2) + p,
	&.section_profile .ic-Layout-contentMain > h2:nth-of-type(2) + p + p,
	&.section_profile .ic-Layout-contentMain > h2:nth-of-type(3),
	&.section_profile .ic-Layout-contentMain > h2:nth-of-type(3) + div,
	table[role="presentation"],
	#print-grades-button,
	#GradeSummarySelectMenuGroup,
	&.section_profile .ic-app-course-menu .eportfolios,
	&.section_profile .ic-app-course-menu .past_global_announcements,
	&.section_profile .ic-app-course-menu .files,
	&.section_about .ic-app-course-menu .files,
	&.section_about .ic-app-course-menu .eportfolios,
	&.section_about .ic-app-course-menu .past_global_announcements,
	&.section_about .ic-app-course-menu [aria-label="Career Hub"],
	&.section_about .ic-app-course-menu [aria-label="Course Calendar"],
	&.section_about .ic-app-course-menu [aria-label="Support"],
	&.section_about .ic-app-course-menu [aria-label="Career Hub"],
	&.section_about .ic-app-course-menu [aria-label="Recordings"]
	{
		display: none!important;
	}
}

.submission-details-header {

	.submission-details-header__time.late {
		color: #0374b5;
	}
	.submission-late-pill {
		display: none;
	} 

}

/***************************
** Course Menu
***************************/
#courseMenuToggle {

	outline: none;
	* { outline: none; }

	color: black!important;
	* { color: black!important; }

	&::-moz-focus-inner { border: 0; }

	box-shadow: none;
}


//.bullet-fun
//{
	// TODO: Replace this missing image
    // list-style-image: url('https://lms.ambi.school/users/106/files/4038/preview?verifier=jk4Ve34tP53iff6OgK8kPXmHpkySSnKvnBzAAxOp');
//}

.bullet-fun li
{margin-bottom: 10px;}

strong
{
	font-weight: 600;
}

.ic-DashboardCard__link {
	text-decoration: none!important;
}

.ic-Login__link
{
	display: block !important;
}

.ic-flash-success
{
	display: none;
}

.ic-DashboardCard__header_hero
{
	box-sizing: border-box;
	height: 146px;
	border: 1px solid rgba(0, 0, 0, 0.1);
	opacity: 0.0;
}

#flash_message_holder
{
	display: none;
}

.ic-app-main-content__secondary
{
	width: auto;
	/* padding-left: 24px; */
}

.ic-flash-info
{
	display: none;}

.ic-Layout-contentMain
{
	margin-top: 35px;}

.lms-sandbox
{
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	padding-right: 29px;
	padding-left: 0px;
}

body
{
	font-size: .9rem !important;
}

.menu-item__text
{
	font-size: 12px !important;
}

.pages.show .page-title
{
	font-size: 1.8em;
	font-weight: 200;
	margin: 15px 0;
	color: #031A2B;
}

.heading-45
{
	font-size: 32px;
	line-height: 42px;
	font-weight: 500;
}

.heading-46
{
	font-size: 17px;
	line-height: 28px;
	font-weight: 600;
}

.paragraph-57
{
	color: #141414;
	font-size: 15px;
	line-height: 24px;
}

.lms-button.purp
{
	margin-top: 25px;
	margin-bottom: 25px;
	border-radius: 5px;
	background-color: #A133C9;
}

.lms-button
{
	padding-right: 34px !important;
	padding-left: 34px !important;
	font-family: Montserrat, sans-serif;
	font-size: 15px;
	font-weight: 500;
	text-decoration: none !important;
}

a.lms-button:hover
{
	color: #FFF !important;
}

.w-button
{
	display: inline-block;
	padding: 9px 15px;
	background-color: #3898EC;
	color: #FFF;
	border: 0;
	line-height: inherit;
	text-decoration: none;
	cursor: pointer;
	border-radius: 0;
}

.w-button a:hover
{
	color: #FFFFFF;}

li, p
{
	color: #031A2B;
	font-weight: 400;
	font-family: 'Poppins', sans-serif;
}

.dms-lms-learning-outcomes
{
	margin-top: 10px;
	margin-bottom: 37px;
	border-top: 1px solid #B4B4B4;
	padding-top: 12px;
	padding-bottom: 30px;
}

.dms-lms-overview-img
{
	margin-left: 54px;
}

.dms-lms-learning-outcomes
{
	border-bottom: 1px solid #B4B4B4;
}

*
{
	font-family: 'Poppins', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
}

/* main css */
h1, h2, h3, h4, h5, p
{
	font-family: 'Poppins', sans-serif;
}

/* login page css */

/*
  Note:
  disabling banner using the command given in bitnami docs is not working.
  Even any modifications to apache is not working. I'm not sure why.
  So, I'm disabling it here.
*/
#bitnami-banner
{
	display: none;
}

video#bgvid
{
	height: auto;
	left: 50%;
	min-height: 100%;
	min-width: 100%;
	position: fixed;
	top: 50%;
	transform: translateX(-50%) translateY(-50%);
	width: auto;
	z-index: -100;
}

.video-dottedoverlay
{
	background-image: url("../assets/images/gridtile2.png");
	background-repeat: repeat;
	height: 100%;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
	z-index: 3;
}


video#bgvid
{
	transition: opacity 1s ease 0s;
}

/* oAuth Confirmation Screen */

.ic-Login-confirmation__logo
{
	width: 140px;
	height: 69px;
	background: url("../assets/images/greenfig-logo.png") no-repeat;
	display: block;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding-left: 350px;
}


.ic-Login-confirmation__header
{
	background: #333;
}

/* End of oAuth stuff */

.ic-Login__content
{
	opacity: .9;

}

.ic-Login-Body {

	min-width: auto!important;

	.ic-app { position: relative; }
	.ic-app:after {
		content: "";
		display: block;
		position: absolute;
		left: 0;
		top: 0;
		background: url(../assets/images/login_bkg.svg) no-repeat !important;
		background-position: 20px -100px;
		background-size: contain;
		@include breakpoint(phone) {
			background-position: bottom!important;
			background-size: contain;
		}
		width: 100%;
		height: 100%;
	}

	.ic-app-main-content { display: block!important; }
	.ic-Layout-contentMain,
	.ic-Login__container {
		margin-top: 0;
	}

	.ic-Login {
		justify-content: flex-end;
		padding-right: 100px;
		@include breakpoint(phone) {
			justify-content: center;
			padding-right: 0;
		}
	}

	.error_text:before {
		display: inline-block;
		content: "Error:";
		padding-right: 5px;
	}

}


.ic-Login-footer .ic-Login-footer__logo-link
{
	display: none;
}

.ic-Login-footer .ic-Login-footer__links
{
	display: none;
}

.ic-Login__link
{
	display: none;
}

.ic-Login-header__links .ic-Login__link:first-of-type
{
	margin-top: 0;
	display: none;
}

.ic-Login-header
{
	display: block;
	padding: 10px 10px 10px 10px;
}

.ic-Login-header__logo
{
	flex: 0 0 300px;
	min-width: 1px;
}

.ic-Login-Body .forgot_password_link {display: none !important;}

.ic-brand-Login-body-bgd-color
{
	background-color: none;
}

.ic-app-footer
{
	display: none;
}

/* end of login page css */


/* Password Set */
.body--login-confirmation {

	min-width: 0!important;

	#main {
		background: url("../assets/images/registration.jpg") top center;
		background-size: cover;
	}	

	.ic-app-main-content,
	.ic-Layout-contentWrapper,
	.ic-Layout-contentMain {		
		height: 100vh;
	}

	.ic-Layout-contentMain {
		display: flex;
		align-items: center;
	}

	.ic-Layout-contentMain {		
		margin: 0;
	}

	.ic-Login-confirmation__content {
		padding: 40px;
	}

	.ic-Login-confirmation {
		margin: 40px auto;
		padding: 0 10px;
		width: auto!important
	}

	form {

		.instructions {
			margin-bottom: 30px;
		}

		.control-label {
			text-align: left;
			width: auto;
		}

		.controls {
			margin-left: 100px;
		}

		input[type="text"], input[type="password"], select {
			width: 100%;
			box-sizing: border-box;
			height: auto;
		}

	}

	/* Stuff to not display */
	.ic-app-main-content__secondary,
	.ic-Login-confirmation__header,
	.control-group:nth-of-type(4),
	.control-group:nth-of-type(5) {
		display: none;
	}

}

/* quizes question css */
.question
{
	background-color: #FFF;
	position: relative;
	min-height: 50px;
	margin: 0.7em auto 30px;
	padding: 24px 32px;
	border: 1px solid #F1F1F1;
	border-radius: 14px;
	box-shadow: 0 10px 20px 0 rgba(50, 62, 99, 0.04);
	margin-bottom: 24px;
}

.question .header
{
	background-color: transparent;
	border: none;
}

.question.multiple_choice_question .answer,
.question.multiple_answers_question .answer,
.question.matching_question .answer,
.question.true_false_question .answer
{
	border: none;
}

/* end of quizzes css */

/* HTML EDITOR ICON FIX css START */

.mce-ico
{

	font-family: tinymce, Arial !important;
}

// -------------------------------------------------------------

@import "section";
@import "overlay";
