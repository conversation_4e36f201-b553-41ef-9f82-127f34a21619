
.modal-overlay
{
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.5);
	transition: opacity 200ms;
	visibility: hidden;
	opacity: 0;
	z-index: 999;
}

.modal-overlay .cancel
{
	position: absolute;
	width: 100%;
	height: 100%;
	cursor: default;
}

.modal-overlay.active,
.modal-overlay:target
{
	visibility: visible;
	opacity: 1;
}

.modal-overlay
{
	.modal
	{
		margin: 100px auto;
		padding: 20px;
		background: #ffffff;
		border: 1px solid #666666;
		width: 300px;
		border-radius: 6px;
		box-shadow: 0 0 50px rgba(0, 0, 0, 0.5);
		position: relative;
	}

	.modal h2
	{ margin-top: 0; }

	.modal .close
	{
		position: absolute;
		width: 20px;
		height: 20px;
		top: 20px;
		right: 20px;
		opacity: 0.8;
		transition: all 200ms;
		font-size: 24px;
		font-weight: bold;
		text-decoration: none;
		color: #777777;
	}

	.modal .close:hover
	{ opacity: 1; }

	.modal .content
	{
		max-height: 400px;
		overflow: auto;
	}

	.modal p
	{
		margin: 0 0 1em;
		text-align: center;
	}

	.modal p:last-child
	{ margin: 0; }
}
