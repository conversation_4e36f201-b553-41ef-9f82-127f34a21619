section, div.section
{
  display: grid;
  grid-gap: 30px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  margin: 0;
  padding: 0;

  .module
  {
    overflow: hidden;
    border-radius: 5px;
    max-width: 370px;
    position: relative;
    box-shadow: 0 0 8px rgba(0, 0, 0, .3);
    z-index: 1;

    .pre
    {
      position: absolute;
      top: 20px;
      left: 20px;
      color: white;
      width: 100%;
    }

    .title
    {
      margin: 0;
      padding: 60px 30px 30px 30px;
      height: 100px;
      background: black;
      color: white;
      font-size: 20px;
      line-height: 27px;
    }

    .button
    {
      height: 40px;
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      text-align: right;

      a
      {
        padding: 10px;
        border-radius: 5px;
        background: black;
        color: white;
        font-size: 14px;
        text-decoration: none!important;

        &:hover, span {
          text-decoration: none!important;
        }

      }

    }

    &.locked
    {

      &:before, &:after {
        position: absolute;
      }

      &:before {
        content: "";
        display: block;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background: rgba(255,255,255,.8);
        z-index: 100;
      }

      &:after {
        content: "";
        display: block;
        width: 50px;
        height: 75px;
        margin-left: -25px;
        left: 50%;
        top: 35%;
        background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMC42NyAxNiI+PGcgaWQ9IkxheWVyXzIiIGRhdGEtbmFtZT0iTGF5ZXIgMiI+PGcgaWQ9IkxheWVyXzEtMiIgZGF0YS1uYW1lPSJMYXllciAxIj48cGF0aCBkPSJNOS44Nyw2LjRIOS42VjMuMkEzLjIxLDMuMjEsMCwwLDAsNi40LDBINC4yN2EzLjIsMy4yLDAsMCwwLTMuMiwzLjJWNi40SC44YS44LjgsMCwwLDAtLjguOHY4YS44LjgsMCwwLDAsLjguOEg5Ljg3YS44LjgsMCwwLDAsLjgtLjh2LThBLjguOCwwLDAsMCw5Ljg3LDYuNFpNMy4yLDMuMkExLjA3LDEuMDcsMCwwLDEsNC4yNywyLjEzSDYuNEExLjA3LDEuMDcsMCwwLDEsNy40NywzLjJWNi40SDMuMloiLz48L2c+PC9nPjwvc3ZnPg==);
        background-repeat: no-repeat;
        z-index: 1000;
      }

      .button
      {
        justify-content: center
      }
    }

  }

  .mod_1 {
    .pre { color: #ED9826; }
    .button a { background:  #ED9826; }
  }
  .mod_2 {
    .pre {color: #4EC8C1;}
    .button a { background:  #4EC8C1; }
  }
  .mod_3 {
    .pre {color: #6A31FF;}
    .button a { background:  #6A31FF; }
  }
  .mod_4 {
    .pre {color: #4EA2C8;}
    .button a { background:  #4EA2C8; }
  }
  .mod_5 {
    .pre {color: #C83D86;}
    .button a { background:  #C83D86; }
  }
  .mod_6 {
    .pre {color: #963DC8;}
    .button a { background:  #963DC8; }
  }
  .mod_7 {
    .pre {color: #EDD226;}
    .button a { background:  #EDD226; }
  }
  .mod_8 {
    .pre {color: #C8724E;}
    .button a { background:  #C8724E; }
  }
  .mod_9 {
    .pre {color: #3DC890;}
    .button a { background:  #3DC890; }
  }
  .mod_10 {
    .pre {color: #90DB26;}
    .button a { background:  #90DB26; }
  }
  .mod_11 {
    .pre {color: #E70034;}
    .button a { background:  #E70034; }
  }

}

/* New Default Override */
body.course_default {

  section, div.section {
    .module {
      .pre {color: #ffffff!important;}
      .button a { background:  #000000!important; }
    }
  }

}

/* DMS Override */
body.course_dms {

  .lms-button {
    background: #D31D46!important;
  }

  section, div.section {
    .module {
      .button a { background:  #D31D46!important; }
    }
  }

}

/* BAS Override */
body.course_bas {

  .lms-button {
    background: #248471!important;
  }

  section, div.section {
    .module {
      .button a { background:  #248471!important; }
    }
  }

}

/* SOPS Override */
body.course_sops {

  .lms-button {
    background: #02809C!important;
  }

  section, div.section {
    .module {
      .button a { background:  #02809C!important; }
    }
  }

}

/* SDS Override */
body.course_sds {

  .lms-button {
    background: #0A4080!important;
  }

  section, div.section {
    .module {
      .button a { background:  #0A4080!important; }
    }
  }

}

/* PMP Override */
body.course_pmp,
body.course_pjm
{

  .lms-button {
    background: #2F195F!important;
  }

  section, div.section {
    .module {
      .button a { background:  #2F195F!important; }
    }
  }

}

/* PDM Override */
body.course_pdm
{
  .lms-button {
    background: #382B9E!important;
  }

  section, div.section {
    .module {
      .button a { background:  #382B9E!important; }
    }
  }
}

/* PDM Override */
body.course_aip
{
  .lms-button {
    background: #15616D!important;
  }

  section, div.section {
    .module {
      .button a { background:  #15616D!important; }
    }
  }
}

/* SFA Override */
body.course_sfa
{
  .lms-button {
    background: #2B449E!important;
  }

  section, div.section {
    .module {
      .button a { background:  #2B449E!important; }
    }
  }
}

/* AIW Override */
body.course_aiw
{
  .lms-button {
    background: #15616D!important;
  }

  section, div.section {
    .module {
      .button a { background:  #15616D!important; }
    }
  }
}

/* Career Hub Override */
body.subsection_pages_career-hub.course_default
{
  section, div.section {
    .module {
      .pre {color: #000000!important;}
      .button a { background:  #005C8F!important; }
    }
  }
}

body .w-button.career-hub-button {
  background:  #005C8F!important;
}
