import { mountThemeScript } from '@/modules/theme'
import { CourseModulesState } from '@/modules/course-modules'

export class Store
{
	localUrl : URL
	env : string
	$! : JQueryStatic
	
	courseDashboard : CourseModulesState
	userRoles = {
		USER: 'user',
		STUDENT: 'student',
		TEACHER: 'teacher',
		ADMIN: 'admin',
		ROOT_ADMIN: 'root_admin'
	}
	currentUserRoles = []
	
	constructor()
	{
		this.localUrl = new URL(window.location.href)
		
		this.env = this.localUrl.host.indexOf('dev') !== -1 || this.localUrl.host.indexOf('staging') !== -1
			? 'testing'
			: 'live'
		
		console.info('Construct Store', this.env)
		
		//console.warn('Local URL', this.localUrl)
		
		this.courseDashboard = new CourseModulesState(this)
	}
	
	isLive() : boolean
	{
		return this.env === 'live'
	}
	
	isTesting() : boolean
	{
		return this.env === 'testing'
	}
	
	rolesValid = () : boolean =>
	{
		return this.currentUserRoles && this.currentUserRoles.length > 0
	}
	
	hasRole = (role : string) : boolean =>
	{
		return this.currentUserRoles && this.currentUserRoles.find(function (r : any) { return r === role }) !== undefined
	}
	
	init() : void
	{
		try
		{
			//co?nsole.info('init')
			this.courseDashboard.init()
		}
		catch(e)
		{
			console.error('store.init', e)
		}
	}

	mount($ : JQueryStatic) : void
	{
		try
		{
			console.warn('mount')
			
			this.$ = $
			
			if(window.ENV && window.ENV.current_user_roles)
			{
				this.currentUserRoles = window.ENV.current_user_roles
				console.info('User roles', this.currentUserRoles)
			}
			else
				console.warn('NO USER ROLES')
			
			mountThemeScript(this)
			this.courseDashboard.mount()
			
			console.warn('mount done')
		}
		catch(e)
		{
			console.error('store.mount', e)
		}
	}
}
