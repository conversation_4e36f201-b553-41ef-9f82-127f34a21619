// Import setMessage.
import { setMessage } from './index';

describe('setMessage', () => {
  describe.each([
    [undefined, 'Hello World and You!'],
    ['<PERSON>', 'Hello World and <PERSON>!'],
    ['<PERSON>', 'Hello World and <PERSON>!'],
  ])('with %s as name', (name: string | undefined, expectedValue: string) => {
    it('sets message correctly', () => {
      const message = setMessage(name);
      expect(message).toBe(expectedValue);
    });
  });
});
