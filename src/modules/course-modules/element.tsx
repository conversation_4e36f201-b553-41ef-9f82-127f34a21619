import { h, Component, render } from 'preact'
import {
	PageReferenceType,
	PageReference
} from '@/models/course-module-type'
import { CourseModulesState } from '@/modules/course-modules/state'

export type ModulesProps = {
	state : CourseModulesState
}

export class ModulesIndex extends Component<ModulesProps>
{
	state = {
		timestamp: null
	}
	
	constructor(props : ModulesProps)
	{
		super(props)
		const { state } = props

		this.setState({
			timestamp: state.timestamp
		})
		
		state.evt.addListener('update', () =>
		{
			console.warn('Update pages event', state.timestamp)
			this.setState({timestamp: state.timestamp})
		})
	}
	
	compare(a : PageReference, b : PageReference) : number
	{
		if(a.position < b.position)
			return -1
		
		if(a.position > b.position)
			return 1
		
		return 0
	}
	
	render() : any
	{
		let count = 1
		
		//const {index} = this.props.state
		//const list : PageReferenceCollection = Object.values(index)
		//list.sort(this.compare)
		
		const { pages } = this.props.state
		
		if(!pages)
			return null
		
		const now = new Date()
		
		return <section>
			{pages.map((m : PageReferenceType) =>
			{
				let locked = false,
					opens = ''
				
				if(m.unlock_at)
				{
					const unlock_at = new Date(m.unlock_at)
					locked = unlock_at !== null && unlock_at > now
					opens = `Opens ${unlock_at.toLocaleDateString()}`
				}
				
				if(!m.url)
					return null
				
				console.info('render', m.prefix)
				
				return <div class={`module mod_${count++}${locked ? ' locked' : ''}`}>
					<div class="pre">{
						m.prefix
					}</div>
					<div class="title">{m.title}</div>
					<div class="button">
						{locked
							? opens
							: <a href={m.url}>Start Now</a>
						}
					</div>
				</div>
			})}
		</section>
	}
}

export const renderModules = (state : CourseModulesState, to : any) : void =>
	to && render(<ModulesIndex state={state}/>, to)
