import {
  CourseModuleCollection,
  CourseModuleType,
  CoursePageCollection,
  CoursePageType,
  PageReference,
  PageReferenceCollection,
  PageReferenceType,
} from "@/models/course-module-type";
import EventEmitter3 from "eventemitter3";
import { renderModules } from "@/modules/course-modules";
import { Store } from "@/store";
import { parseISO, isAfter, isValid } from "date-fns";
import { formatInTimeZone } from "date-fns-tz";

//import coursePages from './coursePages.json'

const apiUrl = process.env.API_URL; // 'https://greenfig-embed-api.herokuapp.com/api' //
console.log("API URL", apiUrl);

interface HashTable<T> {
  [key: string]: T;
}

export class CourseModulesState {
  store: Store;

  public app_id: string;
  public user_id: number | false = false;
  public user_timezone: string = "America/Los_Angeles";

  public course_id: number | false = false;
  public assignment_id: number | false = false;
  //public index : {
  //	[key : string] : PageReference
  //} = {}

  public is_root: boolean = false;
  public is_assignment: boolean = false;
  public is_modules: boolean = false;

  public timestamp?: string;
  public initials?: string;
  public start_at?: string;
  public pages?: PageReferenceCollection;
  public pageIndex: HashTable<PageReference> = {};
  public section?: string;

  public submissions_count: number | false = false;

  public evt: EventEmitter3;

  private _fetch?: Promise<any>;

  protected module_date_overrides: any;

  constructor(store: Store) {
    //console.warn('Construct CourseModulesState')

    this.store = store;

    if (window.ENV && window.ENV.current_user_id) {
      this.user_id = parseInt(window.ENV.current_user_id);
      this.user_timezone = window.ENV.TIMEZONE;

      const parsed = parseISO("2024-04-25T10:00:00Z");
      const dt = formatInTimeZone(
        parsed,
        this.user_timezone,
        "MMM d 'at' h:mma"
      );

      console.info(
        "User data from environment",
        this.user_id,
        this.user_timezone,
        dt
      );
    }

    this.app_id = "canvas-course-modules-container";

    const reg = /^\/courses\/(\d+)(\/?.*)$/;

    const res = reg.exec(store.localUrl.pathname);

    console.info("res", res);

    if (res && res.length) {
      this.course_id = parseInt(res[1]);
      this.is_root = res[2] === "" || res[2] === "/";

      let parts = res[2].split("/");

      if (parts[1] == "assignments" && parts.length >= 3) {
        this.assignment_id = parseInt(parts[2]);
        console.info("assignment_id", this.assignment_id);
        this.is_assignment = true;
      } else if (parts[1] == "modules" && parts.length == 2)
        this.is_modules = true;
    }

    console.info("course_id", this.course_id);
    console.info("is_root", this.is_root);

    // Ad-hoc override module details ---------------------------------
    const override_module_id = this.store.env == "testing" ? 278 : 277;
    console.info(
      "Overriding modules in course",
      override_module_id,
      "for",
      this.store.env
    );
    this.module_date_overrides = {
      [override_module_id]: {
        // TODO: Change to 277 on prod deploy
        "Introduction to Email Marketing": {
          "01": {
            unlock_at: "2024-06-07T17:00:00Z",
          },
          "02": {
            unlock_at: "2024-05-31T17:00:00Z",
          },
        },
        "Marketing Automation": {
          "01": {
            unlock_at: "2024-06-14T17:00:00Z",
          },
          "02": {
            unlock_at: "2024-06-07T17:00:00Z",
          },
        },
        "Social Media Marketing": {
          "01": {
            unlock_at: "2024-06-21T17:00:00Z",
          },
          "02": {
            unlock_at: "2024-06-14T17:00:00Z",
          },
        },
      },
    };
    // -------------------------------------------------------------

    // this.course_id = reg.test(store.localUrl.pathname)
    // 	? parseInt(store.localUrl.pathname
    // 		.replace(reg, '$1'))
    // 	: false

    this.evt = new EventEmitter3();
  }

  isAtLeast126(): boolean {
    if (this.course_id < 126) return false;

    return true;
  }

  isDateAfterCutoffDate(dateString?: string) {
    // Note: date constructor accepts year, month-index (0-based), day (1-based)
    // 2023-08-07 == 2023, 7, 7
    const cutoff = new Date(2023, 7, 7);
    const date = parseISO(dateString || "");

    if (!isValid(date)) {
      console.info("start_date", dateString, "is invalid");
      return false;
    }

    if (!isAfter(date, cutoff)) {
      console.info("start_date", dateString, date, "is", cutoff, "or earlier");
      return false;
    }

    console.info("start_date", dateString, date, "is after cutoff", cutoff);

    return true;
  }

  applyModuleOverrides() {
    if (!this.pages) {
      console.warn(`Pages invalid - can't apply overrides`);
      return;
    }

    if (!this.course_id) {
      console.warn(`No course ID - can't apply overrides`);
      return;
    }

    if (!this.section) {
      console.warn(`No section name - can't apply overrides`);
      return;
    }

    for (let p of this.pages) this.overrideModule(p);
  }

  overrideModule(page: PageReference) {
    if (!this.course_id) return;

    if (!this.module_date_overrides[this.course_id]) {
      console.info(`No overrides for course ${this.course_id}`);
      return;
    }

    const module_index = this.module_date_overrides[this.course_id];

    if (!page.title) {
      console.warn(
        `Can't apply override - page title invalid ${page.title}`,
        page
      );
      return;
    }

    if (!module_index[page.title]) {
      console.info(`No overrides for module ${page.title}`);
      return;
    }

    const section_index = module_index[page.title];

    if (!this.section) return;

    if (!section_index[this.section]) {
      console.info(`No overrides for section ${this.section}`);
      return;
    }

    const override = section_index[this.section];

    console.info(
      `Applying override to course ID: ${this.course_id}, module: ${page.title}, section: ${this.section}`,
      override
    );

    page.unlock_at = override.unlock_at;
    page.override = true;
  }

  init(): Promise<any> {
    console.info("CourseModulesState init", this.course_id);

    if (!this.isAtLeast126()) return Promise.resolve();

    if (this._fetch) return this._fetch;

    return (this._fetch = Promise.all([
      this.fetchCourseData(),
      this.fetchAssignmentData(),
    ]));
  }

  interpolateUserEmail(): void {
    if (!this.user_id) return;

    const url = `${apiUrl}/user/${window.ENV.current_user_id}`;
    fetch(url)
      .then((response) => {
        if (!response.ok)
          throw new Error(`HTTP error! Status: ${response.status}`);
        return response.json();
      })
      .then((data) => {
        const userEmail = data.email;

        if (!userEmail) {
          console.warn("User email not found");
          return;
        }

        document.querySelectorAll('a[data-interpolate="1"]').forEach((link) => {
          const href = link.getAttribute("href") || "";
          const urlSafeEmail = encodeURIComponent(userEmail);
          link.setAttribute("href", href.replace("{EMAIL}", urlSafeEmail));
        });
      })
      .catch((error) => {
        console.error("Error fetching user email:", error);
      });
  }

  mount(): Promise<any> {
    this.mountAdminHelper();

    window.addEventListener("load", () => {
      this.interpolateUserEmail();
    });

    if (this.course_id < 1) {
      console.info("This page does not appear to be related to a course");
      return Promise.resolve();
    }

    return this.init().then(() => {
      this.interpolateUserEmail();

      return Promise.all([
        this.mountCourseDashboard(),
        this.mountCourseModulesPageOverrides(),
      ]);
    });
  }

  mountCourseDashboard(): Promise<any> {
    try {
      const $ = this.store.$,
        app_id = this.app_id;

      console.info("CourseModulesState mount");

      let el: any = null;

      const max_time = 1 * 60 * 1000;
      let timeout = 0;

      const mountRetry = () => {
        try {
          if ($("#unauthorized_message").length) {
            console.info("Page is unauthorized");
            return;
          }

          if (!this.is_root) {
            console.info("This does not appear to be a course index/dashboard");
            return;
          }

          if (!this.isAtLeast126()) {
            console.info("This course ID is excluded");
            return;
          }

          const $content: JQuery<HTMLElement> = $("div#content");

          if ($content.length == 0) {
            if (timeout > max_time) {
              console.warn("Gave up on finding $content");
              return;
            }

            //console.warn('reschedule $content')
            setTimeout(mountRetry, 200);
            timeout += 200;
            return;
          }

          //console.info('A container has been found', $content.get(0))

          const $argh = $(`<div id="${app_id}" />`);

          //console.info('$argh', $argh)

          $content.append($argh);

          el = $argh.get(0);

          //console.warn('el', el)
        } catch (e) {
          console.error("Error", e);
        }
      };

      mountRetry();

      //console.warn('Calling renderModules')

      if (this.isDateAfterCutoffDate(this.start_at)) {
        $("body").addClass(`course_default`);

        if (this.initials) $("body").addClass(`course_${this.initials}`);
      }

      if (el) renderModules(this, el);

      this.mountAssignmentMod();
      this.applySectionMods();

      return Promise.resolve();
    } catch (e) {
      console.error(e);
      return Promise.resolve();
    }
  }

  mountCourseModulesPageOverrides(): Promise<any> {
    try {
      if (!this.is_modules) return Promise.resolve();

      const $ = this.store.$,
        app_id = this.app_id;

      // eslint-disable-next-line @typescript-eslint/no-this-alias
      const self = this;

      console.info("Modules init");
      const $modules = $(".context_module");
      const currentDate = new Date();

      $modules.each(function () {
        const $el = $(this);
        const t = this.ariaLabel;
        const k = `${t?.toUpperCase()}`;
        // console.info('key', k)
        const p = self.pageIndex[k];

        if (p && p.override) {
          if (!p.unlock_at) return;

          console.info("Decorate module block", t, $el);

          const date = parseISO(p.unlock_at);

          if (!isAfter(currentDate, date)) {
            console.info("Locking overridden module block");

            $el.addClass("locked");

            const hour = formatInTimeZone(date, self.user_timezone, "h");
            const meridiem = formatInTimeZone(
              date,
              self.user_timezone,
              "a"
            ).toLowerCase();
            const time = `${hour}${meridiem}`;
            const dt =
              formatInTimeZone(date, self.user_timezone, "MMM d 'at' ") + time;

            $el.find(".unlock_details").show().html(`
				<div class="unlock_at" style="display: none;">${dt}</div>
				Will unlock <span class="displayed_unlock_at" data-tooltip="" data-html-tooltip-title="${dt}">${dt}</span>
			  `);
          } else
            console.info(
              `Override unlock date/time ${p.unlock_at} is not in the future`
            );
        }
      });

      return Promise.resolve();
    } catch (e) {
      console.error(e);
      return Promise.resolve();
    }
  }

  fetchCourseData(force = false): Promise<any> {
    // console.info('CourseModulesState fetchCoursePages')

    const data: any = {
      user_id: this.user_id,
      course_id: this.course_id,
    };
    const params = new URLSearchParams(data);
    const url = `${apiUrl}/data?${params.toString()}`;

    const opts = {
      method: force ? "POST" : "GET",
      headers: {
        Accept: "application/json",
      },
    };

    console.warn("opts.headers", opts.headers);

    return fetch(url, opts)
      .then((response) => {
        if (response.ok) return response.json();
        else throw new Error("HTTP-Error: " + response.status);
      })
      .then(
        (data: {
          timestamp: string;
          initials: string;
          start_at: string;
          pages: PageReferenceCollection;
          section: string;
        }) => {
          const pages: PageReferenceCollection = [];

          for (const page of data.pages) pages.push(new PageReference(page));

          this.timestamp = data.timestamp;
          this.initials = data.initials;
          this.start_at = data.start_at;
          this.pages = pages;
          for (let p of pages) {
            const t = String(p.title);
            this.pageIndex[t] = p;
            this.pageIndex[`${p.prefix}: ${t.toUpperCase()}`] = p;
          }
          console.info("this.pageIndex", this.pageIndex);
          this.section = data.section;

          this.applyModuleOverrides();
        }
      )
      .catch((e) => {
        console.error(e);
      });
  }

  fetchAssignmentData() {
    if (!this.user_id || !this.course_id || !this.assignment_id) {
      console.info(
        "Missing IDs for assignment",
        this.user_id,
        this.course_id,
        this.assignment_id
      );
      return Promise.resolve();
    }

    // console.info('CourseModulesState fetchAssignmentData')

    const url = `${apiUrl}/user/${this.user_id}/course/${this.course_id}/assignment/${this.assignment_id}`;

    const opts = {
      method: "GET",
      headers: {
        Accept: "application/json",
      },
    };

    return fetch(url, opts)
      .then((response) => {
        if (response.ok) return response.json();
        else throw new Error("HTTP-Error: " + response.status);
      })
      .then((data: { submissions_count: number }) => {
        this.submissions_count = data.submissions_count;
      });
  }

  // processPage(course_id : scalar, page : CoursePageType) : PageReference | null
  // {
  // 	let prefix = null,
  // 		title

  // 	//console.warn('page.title', page.title)

  // 	const parts = String(page.title)
  // 		.split(':')
  // 		.filter(n => String(n).trim())

  // 	//console.warn('page parts', parts)

  // 	if(parts.length > 1)
  // 	{
  // 		title = String(parts[1]).trim()
  // 		prefix = String(parts[0]).toUpperCase()
  // 	}
  // 	else
  // 		return null // ignore any pages without a prefix

  // 	const ref = new PageReference

  // 	const url = `/courses/${course_id}/pages/${page.url}`
  // 	ref.set({prefix, title, url})

  // 	//console.warn('processPage', ref)

  // 	return ref
  // }

  mountAdminHelper(): void {
    // Bail out unless we are in a testing environment or user is a TEACHER or ADMIN
    if (
      //this.store.env !== 'testing' && // Uncomment to permit in testing environment
      // !this.store.hasRole(this.store.userRoles.TEACHER) && // Uncomment to permit TEACHERs
      !this.store.hasRole(this.store.userRoles.ADMIN) // Uncomment to permit ADMINs
    ) {
      console.warn("Admin helper not added for this user role");
      return;
    }

    /**
     * div.header-bar-right
     * <a href="#" class="btn"><i class="icon-reset"></i> Force-Reload Auto Course Modules</a>
     */
    const $ = this.store.$;

    const retry = () => {
      const $container = $(".page-toolbar-end .buttons");

      if ($container.length) {
        const $link = $(
          `<a href="#" class="btn force_reload"><i class="icon-reset"></i> Force-Reload Auto Course Modules</a>`
        );
        $container.prepend($link);

        const onClick = async () => {
          $link.prop("disabled", true);
          await this.fetchCourseData(true);
          this.evt.emit("update");
          $link.prop("true", true);
        };

        $link.click(onClick);

        return;
      }

      //console.info('try failed test')

      setTimeout(retry, 1000);
    };

    retry();

    const p = [80, 65, 71, 69, 83];
    let s = 0;

    $(document).on("keydown", async (evt) => {
      evt = evt || window.event;

      if (!evt.ctrlKey) return;

      s = evt.keyCode == p[s] ? s + 1 : 0;

      if (s > 0) {
        console.warn(s);
        evt.preventDefault();
      }

      if (s === p.length) {
        s = 0;

        this.fetchCourseData(true).then(() => this.evt.emit("update"));

        //const index = await this.generatePages()
        //
        //$(document.body).append(`
        //	<div id="canvas-course-modules-index" class="modal-overlay active">
        //		<div class="modal" style="width:50%">
        //			<h2>Constructed Index</h2>
        //			<div class="content">
        //				<textarea rows="15" style="width: 90%"></textarea>
        //			</div>
        //		</div>
        //	</div>
        //`)
        //
        //$('#canvas-course-modules-index textarea')
        //	.text(JSON.stringify(index))
      }
    });
  }

  mountAssignmentMod() {
    if (!this.is_assignment) return;

    console.info(
      "This is an assignment page and submissions_count is",
      this.submissions_count
    );

    const $ = this.store.$;

    if (this.submissions_count > 0) {
      $("body").addClass("assignment-submitted");
    }
  }

  applySectionMods() {
    console.info("applySectionMods");

    if (!this.section) {
      console.warn("Warning: section is not available");
      return;
    }

    console.info("section is", this.section);

    $("#section-tabs li a").each((i, el) => {
      const $el = $(el);

      if ($el.attr("aria-label") == "Recordings")
        $el.attr(
          "href",
          `/courses/${this.course_id}/pages/recordings-${this.section}`
        );
    });

    const updateButtons = () => {
      $("a[data-section]").each((i, el) => {
        let $el = $(el);
        let numeric_section = parseInt(String(this.section), 10);

        console.info(
          "section button",
          $el.data("section"),
          this.section,
          numeric_section
        );

        if (
          $el.data("section") !== numeric_section &&
          $el.data("section") !== this.section
        ) {
          console.info("section hiding", $el);
          $el.hide();
        }
      });
    };

    monitorEventual(
      eventualContentCallback(() => {
        console.info("section eventualContentCallback");
        updateButtons();
      })
    );

    updateButtons();

    console.info("window.location", window.location.pathname);

    let match = window.location.pathname.match(/\/courses\/\d+\/pages\/(.*)/);

    if (!match) return;

    const page = match[1];

    console.info("location match page", page);

    match = page.match(/(\w+)(-\d+)?/);

    if (!match) return;

    const name = match[1];

    console.info("location match name", name);

    if (name === "recordings") {
      console.info("processing 'recordings' overrides");

      let $overrides = function () {
        $("#breadcrumbs span.ellipsible").each((i, el) => {
          const $el = $(el);
          const match = $el.text().match(/^Recordings - ([^ ]+)/);
          if (match) {
            console.info("#breadcrumbs match", match);
            $el.text("Recordings");
          }
        });

        $("title").text("Recordings");
        $("h1.page-title").text("Recordings");
      };

      $overrides();

      monitorEventual(eventualContentCallback($overrides));
    }
  }

  //async generatePages() : Promise<any>
  //{
  //	console.log('Generating.')
  //
  //	const min_course_id = 127,
  //		index : any = {}
  //
  //	const getData = async (course_id : scalar) =>
  //	{
  //		console.log(`Getting pages for course '${course_id}'.`)
  //
  //		const url = `https://learn.greenfig.com/api/v1/courses/${course_id}/pages?per_page=100`
  //		const response = await fetch(url)
  //
  //		if(!response.ok)
  //		{
  //			console.error("HTTP-Error: " + response.status)
  //			return null
  //		}
  //
  //		let text = await response.text()
  //		if(text.match(/^while\(1\);/))
  //			text = text.substr(9)
  //
  //		//console.info('text', text)
  //
  //		// if HTTP-status is 200-299
  //		// get the response body (the method explained below)
  //		const pages : CoursePageCollection = JSON.parse(text)
  //		const refs : PageReferenceCollection = []
  //
  //		for(const page of pages)
  //		{
  //			const ref : PageReference|null = this.processPage(course_id, page)
  //
  //			if(ref)
  //			{
  //				// I do not understand the fukn deal here
  //				//@ts-ignore
  //				refs.push(ref)
  //			}
  //		}
  //
  //		//console.warn('returning refs', refs)
  //
  //		return refs
  //	}
  //
  //	let failures = 0
  //	const max_failures = 10
  //
  //	for(let course_id = min_course_id; course_id < min_course_id+50; course_id++)
  //	{
  //		//console.info('course_id', course_id)
  //
  //		const refs : PageReferenceCollection|null = await getData(course_id)
  //
  //		if(refs)
  //		{
  //			failures = 0
  //
  //			if(!index[course_id])
  //				index[course_id] = {}
  //
  //			for(const ref of refs)
  //			{
  //				//console.warn('ref.prefix', ref.prefix)
  //				if(ref.prefix)
  //					index[course_id][ref.prefix] = ref
  //			}
  //		}
  //		else
  //			failures++
  //
  //		if(failures > max_failures)
  //			break
  //		else if(failures > 0)
  //			console.warn(failures, 'of', max_failures, 'failures')
  //	}
  //
  //	console.info('Final index', index, JSON.stringify(index))
  //
  //	// url = `https://18.211.34.24/api/v1/courses/${course_id}/pages?per_page=100`
  //	// const response = await fetch(url)
  //	// const data = await response.json()
  //	//
  //	// console.info('resoponse', data)
  //
  //	return index
  //}
}

type scalar = string | number;

function monitorEventual(callback: MutationCallback) {
  const observer = new MutationObserver(callback);

  // Configure the observer to watch for changes in the DOM
  const config: MutationObserverInit = {
    childList: true, // Watch for changes in the child elements (e.g., new elements added)
    subtree: true, // Watch for changes in the entire DOM subtree
  };

  // Start observing the DOM with the specified configuration
  observer.observe(document.body, config);
}

const eventualContentCallback =
  (action: () => void): MutationCallback =>
  (mutationsList, observer) => {
    for (const mutation of mutationsList) {
      if (mutation.type === "childList") {
        // Check if the added nodes include an h1 element with the class 'page-title'
        const addedNodes = mutation.addedNodes;
        const nodesArray = Array.from(addedNodes); // Convert NodeList to an array
        for (const node of nodesArray) {
          if (
            node instanceof HTMLDivElement &&
            node.classList.contains("user_content")
          )
            action();
        }
      }
    }
  };
