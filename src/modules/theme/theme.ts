import { Store } from '@/store'

export const mountThemeScript = ({$, hasRole, rolesValid, userRoles, currentUserRoles, localUrl} : Store) =>
{
	const $body = $('body')

	const watchAndReplaceInvalidLoginText = () => {
		const replaceInvalidLoginText = () => {
			const textNode = $('#flash_message_holder .ic-flash-error')
				.contents()
				.filter((_, node) =>
					node.nodeType === 3 && node.nodeValue?.trim() === 'Invalid username or password'
				)
				.first()

			if (textNode.length) {
				textNode[0].nodeValue = 'Error: Invalid email or password'
				$('#flash_message_holder').css('display', 'block')
				return true
			}
			return false
		}

		const targetNode = document.getElementById('flash_message_holder')
		if (!targetNode) return

		if (replaceInvalidLoginText()) return

		const observer = new MutationObserver(() => {
			if (replaceInvalidLoginText()) observer.disconnect()
		})

		observer.observe(targetNode, { childList: true, subtree: true })
	}

	watchAndReplaceInvalidLoginText()

	if(!rolesValid())
	{
		console.warn('mountThemeScript bailing for lack of user roles')
		loaded()
		return
	}

	console.warn('mountThemeScript')

	const currentCanvasURL = 'https://lms.ambi.school'
	const currentAmbiURL = 'https://greenfig.ambi.school'

	const root = new URL(localUrl)
	root.pathname = ''

	const currentLocation = localUrl.pathname
	const isLogout = currentLocation === `${root}/logout`
	const hasLogin = currentLocation.match(/login/g) != null
	const isCourse = currentLocation.match(/courses/g) != null
	const isProfile = currentLocation.match(/profile/g) != null

	const $sectionTabs = $('#section-tabs')
	const $mobileMenuContainer = $('#mobileContextNavContainer')

	let custom_urls :{
		calendar :string
		support :string
		hub :string
		recordings :string
	}

	function buildCustomURLs(course_id : any)
	{
		custom_urls = {
			calendar: '/calendar?include_contexts=course_' + course_id,
			support: '/courses/' + course_id + '/pages/support',
			hub: `/courses/${course_id}/pages/career-hub`,
			recordings: '/courses/' + course_id + '/pages/recordings'
		}
	}

	function addCalendarLinkItem()
	{
		const url = custom_urls.calendar

		if($sectionTabs.children().length < 1)
		{
			console.warn('No other links in menu')
			return
		}

		$sectionTabs.append(
			'<li class="section">' +
			'<a href="' + url + '" aria-label="Calendar" tabindex="99" title="Calendar">Calendar<i role="presentation"></i></a>' +
			'</li>')
	}

	function addHelpLinkItem(active : any)
	{
		const url = custom_urls.support

		if($sectionTabs.children().length < 1)
		{
			console.warn('No other links in menu')
			return
		}

		let className = ''

		if(active)
		{
			$sectionTabs.find('a.active')
				.removeClass('active')

			className = 'active'
		}

		const title = 'Support'
		$sectionTabs.append(
			'<li class="section">' +
			'<a href="' + url + '" aria-label="' + title + '" tabindex="0" title="' + title + '" class="' + className + '">' + title + '<i role="presentation"></i></a>' +
			'</li>')
	}

	function addCareerHubLinkItem(active : any)
	{
		const url = custom_urls.hub

		if($sectionTabs.children().length < 1)
		{
			console.warn('No other links in menu')
			return
		}

		let className = ''

		if(active)
		{
			$sectionTabs.find('a.active')
				.removeClass('active')

			className = 'active'
		}

		const title = 'Career Hub'
		$sectionTabs.append(
			'<li class="section">' +
			'<a href="' + url + '" aria-label="' + title + '" tabindex="0" title="' + title + '" class="' + className + '">' + title + '<i role="presentation"></i></a>' +
			'</li>')
	}

	function addSessionRecordingsItem(active : any)
	{
		const url = custom_urls.recordings

		if($sectionTabs.children().length < 1)
		{
			console.warn('No other links in menu')
			return
		}

		let className = ''

		if(active)
		{
			$sectionTabs.find('a.active')
				.removeClass('active')

			className = 'active'
		}

		const title = 'Recordings'
		$sectionTabs.append(
			'<li class="section">' +
			'<a href="' + url + '" aria-label="' + title + '" tabindex="0" title="' + title + '" class="' + className + '">' + title + '<i role="presentation"></i></a>' +
			'</li>')
	}

	function editMobileMenuItems(active_subsection :string)
	{
		const targetNav = $mobileMenuContainer[0]
    	const observer = new MutationObserver(function(mutationsList)
		{
			mutationsList.forEach(mutation =>
			{
				if (mutation.type === 'childList' && mutation.addedNodes.length > 0)
				{
					const node = mutation.addedNodes[0]
					// console.info(node, node.nodeType, Node.ELEMENT_NODE)

					if (node.nodeType === Node.ELEMENT_NODE)
					{
						const element = node as HTMLElement
						const className = element.getAttribute('class')

						// This is so dumb
						if(className === 'cMIPy_bGBk cMIPy_dTOw')
						{
							// console.log('Finally, the mobile menu is initiallized')
							observer.disconnect()

							// Remove active class from active menu item
							$(element).find('.bEQNl_bdMA')
									  .removeClass('bEQNl_bdMA')

							addMobileMenuItem('calendar', 'Calendar')
							addMobileMenuItem('support', 'Support')
							addMobileMenuItem('recordings', 'Recordings')
							addMobileMenuItem('hub', 'Career Hub')
						}
					}
				}
			})
		})

		const config = {
			childList: true, // Monitor child elements being added/removed
			subtree: true,   // Observe the entire subtree of the targetNav element
		}

		observer.observe(targetNav, config)
		// console.log('listening for mutations on', targetNav)
	}

	function addMobileMenuItem(name :string, label :string)
	{
		// @ts-ignore
		const url = custom_urls[name]

		console.log('addMobileMenuItem', name, label, url)

		const item = `<span class="mobile-menu-item ${name}">
			<a href="${url}" data-cid="Link">
				<span class="mobile-menu-icon"></span>
				<span wrap="normal" letter-spacing="normal" class="mobile-menu-label">${label}</span>
			</a>
		</span>`

		$mobileMenuContainer.find('> span').append(item)
	}

	function loaded()
	{
		$body.addClass('loaded')
	}

	function main()
	{
		try
		{
			if(!currentUserRoles)
			{
				loaded()
				return
			}

			currentUserRoles.forEach(function (user_role : any) {
				$body.addClass('user_role_' + user_role)
			})

			$body.addClass('user_type_' + currentUserRoles[currentUserRoles.length - 1])

			const match = location.pathname.split('/')

			if(!match || match.length === 0)
			{
				console.error('URL not recognized')
				$body.addClass('error')
				return
			}

			let section = match[1]
			const course_id = parseInt(match[2])
			let subsection = 'none'

			if(section === '')
				section = 'root'
			else if(section === 'courses')
			{
				if(course_id)
					section = 'course'

				if(match[3])
					subsection = match.slice(3).join('_')
			}

			$body.addClass('section_' + section)
			$body.addClass('subsection_' + subsection)

			// Hide nav options always?
			$('#global_nav_conversations_link').hide()

			// Add gf_remove body class if any below terms are found withing a .section_course h1
			const terms = [
				'COURSEWORK',
				'PRE-WORK',
				'PRE-Work',
				'PLAYBOOK',
				'TRAILHEAD'
			]
			let hiddenTerms : Array<string> = []
			$('.section_course h1').each(function()
			{
				let text = $(this).text()
				for(let term of terms)
					if(text.match(term))
						hiddenTerms.push(term)
			})
			if(hiddenTerms.length > 0)
			{
				$body.addClass('gf_remove')
				for(let term of hiddenTerms)
					$body.addClass(term.toLowerCase())
			}
			// -------------------------------------------------------

			if(course_id)
			{
				buildCustomURLs(course_id)

				if(hasRole(userRoles.STUDENT))
					addCalendarLinkItem()

				addHelpLinkItem(subsection === 'pages_support')
				addSessionRecordingsItem(subsection === 'pages_recordings')
				addCareerHubLinkItem(subsection === 'pages_career-hub')

				editMobileMenuItems(subsection)
			}
		}
		catch (e : any)
		{
			console.error(e.message, e.stack)
		}
		finally
		{
			loaded()
		}
	}

	main()
}
