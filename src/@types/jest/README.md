# Installation
> `npm install --save @types/jest`

# Summary
This package contains type definitions for Jest (https://jestjs.io/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jest.

### Additional Details
 * Last updated: Tue, 06 Jul 2021 21:33:43 GMT
 * Dependencies: [@types/jest-diff](https://npmjs.com/package/@types/jest-diff), [@types/pretty-format](https://npmjs.com/package/@types/pretty-format)
 * Global values: `afterAll`, `afterEach`, `beforeAll`, `beforeEach`, `describe`, `expect`, `fail`, `fdescribe`, `fit`, `it`, `jasmine`, `jest`, `pending`, `spyOn`, `test`, `xdescribe`, `xit`, `xtest`

# Credits
These definitions were written by [<PERSON><PERSON> (https://asana.com)
//                 Ivo <PERSON>ratev](https://github.com/NoHomey), [jw<PERSON>](https://github.com/jwbay), [<PERSON><PERSON>](https://github.com/asvet<PERSON><PERSON>), [<PERSON>](https://github.com/alexjoverm), [<PERSON> Lukwago](https://github.com/epicallan), [Ika](https://github.com/ikatyang), [Waseem Dahman](https://github.com/wsmd), [Jamie Mason](https://github.com/JamieMason), [Douglas Duteil](https://github.com/douglasduteil), [Ahn](https://github.com/ahnpnl), [Josh Goldberg](https://github.com/joshuakgoldberg), [Jeff Lau](https://github.com/UselessPickles), [Andrew Makarov](https://github.com/r3nya), [Martin Hochel](https://github.com/hotell), [Sebastian Sebald](https://github.com/sebald), [Andy](https://github.com/andys8), [Antoine Brault](https://github.com/antoinebrault), [Gregor Stamać](https://github.com/gstamac), [ExE Boss](https://github.com/ExE-Boss), [Alex Bolenok](https://github.com/quassnoi), [Mario Beltrán Alarcón](https://github.com/Belco90), [Tony Hallett](https://github.com/tonyhallett), [Jason Yu](https://github.com/ycmjason), [Devansh Jethmalani](https://github.com/devanshj), [Pawel Fajfer](https://github.com/pawfa), [Regev Brody](https://github.com/regevbr), and [Alexandre Germain](https://github.com/gerkindev).
