{"name": "@types/jest", "version": "26.0.24", "description": "TypeScript definitions for Jest", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jest", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON> (https://asana.com)\n//                 Ivo Stratev", "url": "https://github.com/NoHomey", "githubUsername": "NoHomey"}, {"name": "jwbay", "url": "https://github.com/jwbay", "githubUsername": "jwbay"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/asvet<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/alexjoverm", "githubUsername": "alexjoverm"}, {"name": "<PERSON>", "url": "https://github.com/epicallan", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ikatyang", "githubUsername": "ikatyang"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/wsmd", "githubUsername": "wsmd"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/douglasduteil", "githubUsername": "douglasduteil"}, {"name": "Ahn", "url": "https://github.com/ahnpnl", "githubUsername": "ahnpnl"}, {"name": "<PERSON>", "url": "https://github.com/joshuak<PERSON>berg", "githubUsername": "joshua<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/UselessPickles", "githubUsername": "UselessPickles"}, {"name": "<PERSON>", "url": "https://github.com/r3nya", "githubUsername": "r3nya"}, {"name": "<PERSON>", "url": "https://github.com/hotell", "githubUsername": "hotell"}, {"name": "<PERSON>", "url": "https://github.com/sebald", "githubUsername": "sebald"}, {"name": "<PERSON>", "url": "https://github.com/andys8", "githubUsername": "andys8"}, {"name": "<PERSON>", "url": "https://github.com/antoinebrault", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/gstamac", "githubUsername": "gstamac"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss", "githubUsername": "ExE-Boss"}, {"name": "<PERSON>", "url": "https://github.com/quassnoi", "githubUsername": "quassnoi"}, {"name": "<PERSON>", "url": "https://github.com/Belco90", "githubUsername": "Belco90"}, {"name": "<PERSON>", "url": "https://github.com/tonyhallett", "githubUsername": "tony<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ycmjason", "githubUsername": "ycmjas<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/devanshj", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/regevbr", "githubUsername": "regevbr"}, {"name": "<PERSON>", "url": "https://github.com/gerkindev", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jest"}, "scripts": {}, "dependencies": {"jest-diff": "^26.0.0", "pretty-format": "^26.0.0"}, "typesPublisherContentHash": "a0a643015335ff74aff8f83ef2952bcc80a1de9559f77e2a8a5f662e98634d64", "typeScriptVersion": "3.8"}