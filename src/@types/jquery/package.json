{"name": "@types/jquery", "version": "3.5.14", "description": "TypeScript definitions for jquery", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jquery", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/leonard-thieu", "githubUsername": "le<PERSON><PERSON>-thieu"}, {"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/choffmeister", "githubUsername": "choff<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>ei", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tasoili", "githubUsername": "tasoili"}, {"name": "<PERSON>", "url": "https://github.com/seanski", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Guuz", "githubUsername": "G<PERSON>uz"}, {"name": "<PERSON>", "url": "https://github.com/ksummerlin", "githubUsername": "k<PERSON><PERSON>lin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/basarat", "githubUsername": "basarat"}, {"name": "<PERSON>", "url": "https://github.com/nwolverson", "githubUsername": "n<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/derekcicerone", "githubUsername": "derekcicerone"}, {"name": "<PERSON>", "url": "https://github.com/AndrewGaspar", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/seikichi", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/benjaminjackman", "githubUsername": "benjamin<PERSON>man"}, {"name": "<PERSON>", "url": "https://github.com/JoshStrobl", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/DickvdBrink", "githubUsername": "DickvdBrink"}, {"name": "<PERSON>", "url": "https://github.com/King2500", "githubUsername": "King2500"}, {"name": "<PERSON>", "url": "https://github.com/terrymun", "githubUsername": "terrymun"}, {"name": "<PERSON>", "url": "https://github.com/martin-badin", "githubUsername": "martin-badin"}, {"name": "<PERSON>", "url": "https://github.com/princefishthrower", "githubUsername": "princefishthrower"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jquery"}, "scripts": {}, "dependencies": {"@types/sizzle": "*"}, "typesPublisherContentHash": "9f788e87c9a414e61c159cc18d7f06aa9d6fffa10bda4e9b5784798949e9eb25", "typeScriptVersion": "3.8"}