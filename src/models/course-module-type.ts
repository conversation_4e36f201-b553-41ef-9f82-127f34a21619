export type CourseModuleCollection = [] | Array<CourseModuleType>

export interface CourseModuleType
{
	id : number // 605
	items_count : number // 2
	items_url : string // "https://18.211.34.24/api/v1/courses/93/modules/605/items"
	name : string // "Week 1: Introduction to Digital Marketing Science"
	position : number // 2
	prerequisite_module_ids : Array<string> // Array []
	publish_final_grade : boolean // false
	published : boolean // true
	require_sequential_progress : boolean // false
	unlock_at : string // "2021-04-26T17:00:00Z"
}

export type CoursePageCollection = [] | Array<CoursePageType>

export interface CoursePageType
{
	page_id : number,
	"title" : string,
	"created_at" : string // "2021-05-14T18:28:26Z",
	"url" : string //"week-1-introduction-to-digital-marketing",
	"editing_roles" : string // "teachers",
	"last_edited_by": {
		"id" : number,
		"display_name" : string // "<PERSON>",
		"avatar_image_url" : string // "https://18.211.34.24/images/messages/avatar-50.png",
		"html_url" : string // "https://18.211.34.24/courses/93/users/1389",
	},
	"published" : boolean,
	"hide_from_students" : boolean,
	"front_page" : boolean,
	"html_url" : string // "https://18.211.34.24/courses/93/pages/week-1-introduction-to-digital-marketing",
	"todo_date" : string|null,
	"updated_at" : string // "2022-01-27T00:23:56Z",
	"locked_for_user" : boolean
}

// ---

export type PageReferenceCollection = Array<PageReference>

export interface PageReferenceType
{
	prefix : string|null
	title : string|null
	unlock_at : string|null
	url : string
	position : number
}

export class PageReference implements PageReferenceType
{
	prefix :string|null = null
	title :string|null = null
	unlock_at :string|null = null
	url :string = ''
	position :number = 0
	override :boolean = false
	
	constructor(opts : any = null)
	{
		if(opts)
			this.set(opts)
	}
	
	set(opts : any) : void
	{
		//console.info('set', opts)

		const filtered : any = {}
		
		for(const i in opts)
			if(Object.prototype.hasOwnProperty.call(this, i))
				filtered[i] = opts[i]
		
		Object.assign(this, filtered)
	}
}
