module.exports = {
	presets: [
		['@babel/preset-env', {
			useBuiltIns: 'usage',
			corejs: {
				version: 3,
				proposals: true,
			},
			loose: true,
		}],
		['@babel/typescript', {
			jsxPragma: "h"
		}]
	],
	plugins: [
		["@babel/transform-react-jsx", { pragma: "h" }],
		['@babel/plugin-proposal-class-properties'],
		['@babel/plugin-proposal-private-methods',
			{
				loose: false,
			},
		],
		['@babel/plugin-proposal-private-property-in-object',
			{
				loose: false
			}
		]
	],
}
