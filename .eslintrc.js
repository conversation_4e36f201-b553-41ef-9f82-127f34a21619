const path = require('path')

module.exports = {
	root: true,
	parser: '@typescript-eslint/parser',
	plugins: ['@typescript-eslint'],
	env: {
		es6: true,
		jest: true,
		node: true,
		browser: true,
	},
	extends: [
		'eslint:recommended',
		'plugin:@typescript-eslint/eslint-recommended',
		'plugin:@typescript-eslint/recommended',
		'plugin:jest-formatting/recommended',
	],
	parserOptions: {
		ecmaVersion: 2018,
		sourceType: 'module',
		tsconfigRootDir: __dirname,
		createDefaultProgram: true,
		project: path.resolve(__dirname, './tsconfig.json'),
		ecmaFeatures: {
			jsx: true,
		},
	},
	rules: {
		quotes: 'off',
		'no-empty': 'off',
		'no-unused-vars': 'off',
		'no-cond-assign': 'off',
		'@typescript-eslint/no-empty-function': 'off',
		'@typescript-eslint/no-unused-vars': 'off',
		'@typescript-eslint/no-explicit-any': 'off',
		'@typescript-eslint/no-inferrable-types': 'off',
		'@typescript-eslint/ban-ts-comment': 'off',
		"no-mixed-spaces-and-tabs": [1, "smart-tabs"],
		"prefer-const": [1, {
			"destructuring": "any",
			"ignoreReadBeforeAssign": false
		}],
	},
	overrides: [
		{
			files: ['*.json'],
			rules: {
				quotes: ['error', 'double'],
			},
		},
		{
			files: ['*.js', '*.jsx'],
			rules: {
				'@typescript-eslint/no-var-requires': 0,
				'@typescript-eslint/explicit-module-boundary-types': 0,
			},
		}
	],
}
